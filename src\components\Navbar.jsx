"use client";
import Link from "next/link";
import React from "react";
import { FiEdit } from "react-icons/fi";

const Navbar = () => {
  const handleNewConversation = () => {
    window.location.reload();
  };

  const handleHomeClick = (e) => {
    e.preventDefault();
    window.location.reload();
  };

  return (
    <nav className="bg-white sticky top-0 z-50">
      <div className="flex justify-between items-center h-15 px-4">
        <Link href="/" className="flex-shrink-0" onClick={handleHomeClick}>
          <span className="text-[17px] font-bold text-black ">Driply.me</span>
        </Link>
        <div className="flex items-center">
          <button
            onClick={handleNewConversation}
            className="px-4 py-2 rounded-lg cursor-pointer"
          >
            <FiEdit className="hidden md:inline text-black text-xl" />
            <FiEdit className="inline md:hidden text-black text-xl" />
          </button>
        </div>
      </div>
      <div className="border-b border-[#F2F2F2]"></div>
    </nav>
  );
};

export default Navbar;
