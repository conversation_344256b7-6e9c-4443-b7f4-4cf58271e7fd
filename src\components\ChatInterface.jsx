"use client";
import React, { useState, useEffect, useRef } from "react";
import { SUGGESTION_CARDS } from "../utils/constants";
import { FaArrowUp, FaChevronLeft, FaChevronRight } from "react-icons/fa6";
import Image from "next/image";
import logo from "../assets/images/logo.png";

const ChatInterface = () => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  // State to track if current screen is mobile (< 768px width)
  const [isMobile, setIsMobile] = useState(false);
  // State to track if user is typing (for suggestion cards fade-out)
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const carouselRef = useRef(null);
  const inputRef = useRef(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim()) {
      setMessages((prev) => [
        ...prev,
        { text: message.trim(), timestamp: Date.now() },
      ]);
      setMessage("");
      setIsTyping(false); // Reset typing state after sending message
      // Reset textarea height after sending message
      if (inputRef.current) {
        inputRef.current.style.height = "104px";
      }
    }
  };

  // Handle textarea auto-resize
  const handleTextareaResize = (textarea) => {
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = "auto";
      // Set height to scrollHeight, but with minimum of 104px
      const newHeight = Math.max(104, textarea.scrollHeight);
      textarea.style.height = `${newHeight}px`;
    }
  };

  // Handle input change with typing state management and auto-resize
  const handleInputChange = (e) => {
    const value = e.target.value;
    setMessage(value);

    // Auto-resize textarea
    handleTextareaResize(e.target);

    // Set typing state based on whether there's content and no messages yet
    if (messages.length === 0) {
      setIsTyping(value.length > 0);
    }
  };

  // Handle key press for Enter behavior
  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      // Enter without Shift submits the form
      e.preventDefault();
      handleSubmit(e);
    }
    // Enter with Shift creates a new line (default textarea behavior)
  };

  // Handle suggestion card click - automatically start conversation
  const handleSuggestionClick = (cardTitle) => {
    setMessages((prev) => [
      ...prev,
      { text: cardTitle, timestamp: Date.now() },
    ]);
    setMessage(""); // Clear input field
    setIsTyping(false); // Reset typing state
  };

  // Calculate maximum slides based on screen size
  // Mobile: Show 1 card per slide (total cards = total slides)
  // Tablet/Desktop: Show 2 cards per slide (total slides = cards / 2)
  const getMaxSlides = () => {
    if (isMobile) return SUGGESTION_CARDS.length;
    return Math.ceil(SUGGESTION_CARDS.length / 2);
  };
  const nextSlide = () => {
    const maxSlides = getMaxSlides();
    setCurrentSlide((prev) => (prev + 1) % maxSlides);
  };
  const prevSlide = () => {
    const maxSlides = getMaxSlides();
    setCurrentSlide((prev) => (prev - 1 + maxSlides) % maxSlides);
  };

  // Manual navigation functions
  const handleNextSlide = () => {
    nextSlide();
  };
  const handlePrevSlide = () => {
    prevSlide();
  };

  // Touch handlers for mobile/tablet swipe gestures on carousel
  const handleTouchStart = (e) => {
    setTouchStart(e.targetTouches[0].clientX);
  };
  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const swipeDistance = touchStart - touchEnd;
    const minSwipeDistance = 50; // Reduced from 75px for better responsiveness

    // Swipe left (next slide) - minimum 50px swipe distance
    if (swipeDistance > minSwipeDistance) {
      nextSlide();
    }
    // Swipe right (previous slide) - minimum 50px swipe distance
    if (swipeDistance < -minSwipeDistance) {
      prevSlide();
    }

    // Reset touch values
    setTouchStart(0);
    setTouchEnd(0);
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Screen size detection and responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      if (typeof window !== "undefined") {
        // Mobile breakpoint: < 768px (matches Tailwind's md breakpoint)
        setIsMobile(window.innerWidth < 768);
      }
    };

    checkMobile();

    const handleResize = () => {
      // Reset carousel to first slide when screen size changes
      setCurrentSlide(0);
      checkMobile();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  // Auto-focus input on desktop and tablet only (not mobile)
  useEffect(() => {
    if (inputRef.current && messages.length === 0) {
      // Only auto-focus on desktop and tablet (screen width >= 768px)
      const shouldAutoFocus = window.innerWidth >= 768;
      if (shouldAutoFocus) {
        // Small delay to ensure the component is fully rendered
        const timer = setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
        return () => clearTimeout(timer);
      }
    }
  }, [messages.length]);

  return (
    <div className=" bg-white flex flex-col">
      {/* DESKTOP LAYOUT (lg and above - ≥1024px) */}
      <div className="hidden lg:flex flex-1 flex-col px-4 py-7">
        {messages.length === 0 ? (
          /* DESKTOP: Initial state - centered layout with logo, title, input, and carousel */
          <div className="flex flex-col items-center justify-center flex-1 ">
            <div className="flex flex-col items-center w-[768px]">
              {/* DESKTOP: Business Logo and Name */}
              <div className="flex flex-col items-center mb-2">
                <Image
                  src={logo}
                  alt="Arik photography logo"
                  className="w-30 h-30  rounded-full object-cover  flex items-center justify-center mb-2"
                />
                <h2 className="text-2xl font-semibold text-gray-800 ">
                  Arik photography
                </h2>
              </div>

              {/* DESKTOP: Main title */}
              <h1 className="text-4xl text-gray-900 mb-6 text-center mt-14">
                Ready when you are.
              </h1>

              {/* DESKTOP: Input form - centered, 768px width, 104px height */}
              <form onSubmit={handleSubmit} className="relative w-full mb-6">
                <textarea
                  ref={messages.length === 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  autoFocus={messages.length === 0}
                  rows={1}
                  className="pt-3 px-4 pb-16 pr-12 w-full min-h-[104px] text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md resize-none overflow-hidden"
                  style={{ height: "104px" }}
                />
                <button
                  type="submit"
                  className="absolute right-3 bottom-7 w-9 h-9 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800"
                >
                  <FaArrowUp className="w-4 h-4" />
                </button>
              </form>

              {/* DESKTOP: Suggestion cards carousel - shows 2 cards per slide */}
              <div
                className={`relative w-full max-w-2xl transition-opacity duration-300 ease-in-out ${
                  isTyping ? "opacity-0 pointer-events-none" : "opacity-100"
                }`}
              >
                <div className="overflow-hidden">
                  <div
                    ref={carouselRef}
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                  >
                    {/* DESKTOP: Create slides with 2 cards each */}
                    {Array.from({
                      length: Math.ceil(SUGGESTION_CARDS.length / 2),
                    }).map((_, slideIndex) => (
                      <div
                        key={slideIndex}
                        className="w-full flex-shrink-0 flex gap-2"
                      >
                        {/* DESKTOP: Show 2 cards side by side per slide */}
                        {SUGGESTION_CARDS.slice(
                          slideIndex * 2,
                          slideIndex * 2 + 2
                        ).map((card, cardIndex) => (
                          <div
                            key={slideIndex * 2 + cardIndex}
                            className="flex-1"
                          >
                            <button
                              onClick={() => handleSuggestionClick(card.title)}
                              className="w-full py-4 px-5 rounded-[12px] bg-[#f6f6f6] text-left group   transition-all duration-200"
                            >
                              <div className="text-[16px] font-[600] text-black mb-1">
                                {card.title}
                              </div>
                              <div className="text-[16px] text-gray-500">
                                {card.subtitle}
                              </div>
                            </button>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
                {/* DESKTOP: Navigation arrows - positioned outside carousel */}
                <button
                  onClick={handlePrevSlide}
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-12 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-12 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* DESKTOP: Chat state - messages displayed with fixed input at bottom */
          <>
            {/* DESKTOP: Messages container - scrollable area with proper spacing */}
            <div className="flex-1 overflow-y-auto pb-32 pt-8">
              <div className="w-full lg:w-[768px] mx-auto px-4">
                <div className="space-y-4">
                  {messages.map((msg, index) => (
                    <div key={index} className="flex justify-end">
                      <div className="bg-gray-100 px-4 py-2 rounded-3xl max-w-xs lg:max-w-lg break-words">
                        {msg.text}
                      </div>
                    </div>
                  ))}
                </div>
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* DESKTOP: Fixed input at bottom - 768px width, 104px height */}
            <div className="fixed bottom-0 left-0 right-0 p-4">
              <form
                onSubmit={handleSubmit}
                className="relative w-full lg:w-[768px] mx-auto"
              >
                <textarea
                  ref={messages.length > 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  rows={1}
                  className="w-full min-h-[104px] pt-3 md:px-5 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-lg resize-none overflow-hidden"
                  style={{ height: "104px" }}
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors duration-200"
                >
                  <FaArrowUp className="w-4 h-4" />
                </button>
              </form>
              {/* DESKTOP: Powered by text */}
              <div className="text-center mt-2">
                <p className="text-xs text-gray-500">This chat is powered by <Driply.me</p>
              </div>
            </div>
          </>
        )}
      </div>

      {/* MOBILE & TABLET LAYOUT (below lg - <1024px) */}
      <div className="lg:hidden h-screen overflow-hidden fixed inset-0 flex flex-col  mt-5">
        {messages.length === 0 ? (
          /* MOBILE/TABLET: Initial state - logo at top, main title centered */
          <>
            {/* MOBILE/TABLET: Logo and business name at top */}
            <div className="flex flex-col items-center pt-15  pb-4 px-4 ">
              {/* MOBILE ONLY: Business Logo and Name (hidden on tablet) */}
              <div className="flex flex-col items-center md:hidden ">
                <Image
                  src={logo}
                  alt="Arik photography logo"
                  className="w-30 h-30 object-cover rounded-full flex items-center justify-center mb-3"
                />
                <h2 className="text-xl font-semibold text-gray-800">
                  Arik photography
                </h2>
              </div>

              {/* TABLET ONLY: Business Logo and Name (hidden on mobile and desktop) */}
              <div className="hidden md:flex lg:hidden flex-col items-center">
                <Image
                  src={logo}
                  alt="Arik photography logo"
                  className="w-30 h-30 rounded-full flex object-cover items-center justify-center mb-3"
                />
                <h2 className="text-xl font-semibold text-gray-800">
                  Arik photography
                </h2>
              </div>
            </div>

            {/* MOBILE/TABLET: Main title centered in middle of remaining space */}
            <div className="flex flex-col items-center justify-center flex-1 px-4" style={{ paddingBottom: '260px' }}>
              <h1 className="text-2xl md:text-4xl text-gray-900 text-center leading-relaxed">
                How can I help you?
              </h1>
            </div>
          </>
        ) : (
          /* MOBILE/TABLET: Chat state - messages with fixed input overlay */
          <>
            {/* MOBILE/TABLET: Messages container - messages flow upward from input */}
            <div className="flex-1 overflow-y-auto bg-white" style={{ height: 'calc(100vh - 160px)', paddingTop: '80px' }}>
              <div className="w-full max-w-[803px] mx-auto px-4 pb-6">
                <div className="space-y-4">
                  {messages.map((msg, index) => (
                    <div key={index} className="flex justify-end">
                      <div className="bg-gray-100 px-4 py-2 rounded-3xl max-w-xs break-words text-sm">
                        {msg.text}
                      </div>
                    </div>
                  ))}
                </div>
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* MOBILE/TABLET: Fixed input overlay - matches the bottom section height */}
            <div className="absolute bottom-0 left-0 right-0 bg-white" style={{ height: '160px' }}>
              <div className="px-4 py-4">
                <form onSubmit={handleSubmit} className="relative">
                  <div className="relative w-full max-w-[890px] mx-auto">
                    <textarea
                      value={message}
                      onChange={handleInputChange}
                      onKeyDown={handleKeyPress}
                      placeholder="Ask anything"
                      rows={1}
                      className="w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-hidden"
                      style={{ height: "104px" }}
                    />
                    <button
                      type="submit"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800"
                    >
                      <FaArrowUp className="w-4 h-4" />
                    </button>
                  </div>
                </form>
                {/* MOBILE/TABLET: Powered by text */}
                <div className="text-center mb-5">
                  <p className="text-xs text-gray-500">This chat is powered by Driply.me</p>
                </div>
              </div>
            </div>
          </>
        )}

        {/* MOBILE/TABLET: Fixed Bottom Section - contains carousel and input */}
        <div className="absolute bottom-0 left-0 right-0 bg-white" style={{ height: '260px' }}>
          {messages.length === 0 && (
            /* MOBILE/TABLET: Suggestion cards carousel section */
            <div
              className={`px-4 pt-4 pb-2 transition-opacity duration-300 ease-in-out ${
                isTyping ? "opacity-0 pointer-events-none" : "opacity-100"
              }`}
            >
              <div className="relative max-w-2xl mx-auto">
                <div className="overflow-hidden">
                  <div
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{
                      transform: isMobile
                        ? `translateX(-${currentSlide * 70}%)`
                        : `translateX(-${currentSlide * 100}%)`,
                    }}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                  >
                    {isMobile
                      ? /* MOBILE: Show 1 full card + 1 half card per slide */
                        SUGGESTION_CARDS.map((card, index) => (
                          <div
                            key={index}
                            className="flex-shrink-0"
                            style={{
                              width: "70%",

                            }}
                          >
                            <div className="px-1">
                              <button
                                onClick={() =>
                                  handleSuggestionClick(card.title)
                                }
                                className="w-full p-3 rounded-[12px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200"
                              >
                                <div className="text-[13px] font-[600] text-black mb-1">
                                  {card.title}
                                </div>
                                <div className="text-[12px] text-gray-500">
                                  {card.subtitle}
                                </div>
                              </button>
                            </div>
                          </div>
                        ))
                      : /* TABLET: Show 2 cards per slide in grid layout */
                        Array.from({
                          length: Math.ceil(SUGGESTION_CARDS.length / 2),
                        }).map((_, slideIndex) => (
                          <div
                            key={slideIndex}
                            className="w-full flex-shrink-0 grid grid-cols-2 gap-3"
                          >
                            {SUGGESTION_CARDS.slice(
                              slideIndex * 2,
                              slideIndex * 2 + 2
                            ).map((card, cardIndex) => (
                              <button
                                key={slideIndex * 2 + cardIndex}
                                onClick={() =>
                                  handleSuggestionClick(card.title)
                                }
                                className="p-3 rounded-[12px] bg-[#F6F6F6] text-left group hover:bg-[#D6EDFF] transition-all duration-200"
                              >
                                <div className="text-[14px] font-[600] text-black mb-1">
                                  {card.title}
                                </div>
                                <div className="text-[13px] text-gray-500">
                                  {card.subtitle}
                                </div>
                              </button>
                            ))}
                          </div>
                        ))}
                  </div>
                </div>

                {/* TABLET ONLY: Navigation arrows - hidden on mobile, shown on tablet */}
                <button
                  onClick={handlePrevSlide}
                  className="hidden md:flex absolute -left-10 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 z-10"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="hidden md:flex absolute -right-10 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 z-10"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          )}

          {/* MOBILE/TABLET: Input form section - always visible at bottom */}
          <div className="px-4 py-4 absolute bottom-0 left-0 right-0 ">
            <form onSubmit={handleSubmit} className="relative">
              <div className="relative w-full max-w-[890px] mx-auto">
                <textarea
                  ref={messages.length === 0 ? inputRef : null}
                  value={message}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyPress}
                  placeholder="Ask anything"
                  rows={1}
                  className="w-full min-h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base resize-none overflow-hidden"
                  style={{ height: "104px" }}
                />
                <button
                  type="submit"
                  className="absolute right-3 top-[60%] transform -translate-y-1/2 w-9 h-9 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800"
                >
                  <FaArrowUp className="w-4 h-4" />
                </button>
              </div>
            </form>
            {/* MOBILE/TABLET: Powered by text */}
            <div className="text-center mb-3">
              <p className="text-xs text-gray-500">This chat is powered by Driply.me</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
